import { _decorator, Component, Node } from "cc";
import { GameData } from "../../GameScrpit/game/GameData";
import data from "../../GameScrpit/lib/data/data";
const { ccclass, property } = _decorator;

@ccclass("TimeTestModule")
export class TimeTestModule extends Component {
  // 私有构造函数，防止外部实例化
  private constructor() {
    super();
  }

  // 单例模式
  public static get instance(): TimeTestModule {
    if (!GameData.instance.TimeTestModule) {
      GameData.instance.TimeTestModule = new TimeTestModule();
    }
    return GameData.instance.TimeTestModule;
  }

  //初始化数据

  private _data = new TimeTestData();
  private _api = new TimeTestApi();
  private _service = new TimeTestService();
  private _subscriber = new TimeTestSubscriber();
  private _route = new TimeTestRoute();
  private _viewModel = new TimeTestViewModel();
  private _config = new TimeTestConfig();

  public static get data() {
    return this.instance._data;
  }
  public static get api() {
    return this.instance._api;
  }
  public static get service() {
    return this.instance._service;
  }
  public static get subscriber() {
    return this.instance._subscriber;
  }
  public static get route() {
    return this.instance._route;
  }
  public static get viewModel() {
    return this.instance._viewModel;
  }
  public static get config() {
    return this.instance._config;
  }

  public init(data?: any, completedCallback?: Function) {
    if (this._subscriber) {
      this._subscriber.unRegister();
    }
    this._data = new TimeTestData();
    this._api = new TimeTestApi();
    this._service = new TimeTestService();
    this._subscriber = new TimeTestSubscriber();
    this._route = new TimeTestRoute();
    this._viewModel = new TimeTestViewModel();
    this._config = new TimeTestConfig();
    //模块初始化
    this._subscriber.register();
    this._route.init();
    TimeTestModule.api.timeTestInfo(() => {
      completedCallback && completedCallback();
    });
  }
}
