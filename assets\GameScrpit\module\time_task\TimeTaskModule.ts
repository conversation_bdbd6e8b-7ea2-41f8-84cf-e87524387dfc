import { GameData } from "../../game/GameData";
import data from "../../lib/data/data";
import { TimeTaskApi } from "./TimeTaskApi";
import { TimeTaskConfig } from "./TimeTaskConfig";
import { TimeTaskData } from "./TimeTaskData";
import { TimeTaskRoute } from "./TimeTaskRoute";
import { TimeTaskService } from "./TimeTaskService";
import { TimeTaskSubscriber } from "./TimeTaskSubscriber";
import { TimeTaskViewModel } from "./TimeTaskViewModel";

export class TimeTaskModule extends data {
  // 私有化构造函数 防止外部实例化 作用 : 防止外部实例化 作用 : 防止外部实例化
  private constructor() {
    super();
  }
  // 单例模式 作用: 确保模块只有一个实例
  public static get instance(): TimeTaskModule {
    // 如果实例不存在 则创建实例
    if (!GameData.instance.TimeTaskModule) {
      // 创建实例
      GameData.instance.TimeTaskModule = new TimeTaskModule();
    }
    // 返回实例
    return GameData.instance.TimeTaskModule;
  }
  //初始化 作用 : 初始化 模块
  private _data = new TimeTaskData();
  private _api = new TimeTaskApi();
  private _service = new TimeTaskService();
  private _subscriber = new TimeTaskSubscriber();
  private _route = new TimeTaskRoute();
  private _viewModel = new TimeTaskViewModel();
  private _config = new TimeTaskConfig();

  // 模块数据 作用 : 存储模块数据
  public static get data() {
    return this.instance._data;
  }

  public static get api() {
    return this.instance._api;
  }

  public static get config() {
    return this.instance._config;
  }

  public static get service() {
    return this.instance._service;
  }

  public static get viewModel() {
    return this.instance._viewModel;
  }

  // 初始化 作用 : 初始化模块
  public init(data?: any, completedCallback?: Function) {
    // 如果实例存在 则销毁实例
    if (this._subscriber) {
      // 销毁实例
      this._subscriber.unRegister();
    }
    this._data = new TimeTaskData();
    this._api = new TimeTaskApi();
    this._service = new TimeTaskService();
    this._subscriber = new TimeTaskSubscriber();
    this._route = new TimeTaskRoute();
    this._viewModel = new TimeTaskViewModel();
    this._config = new TimeTaskConfig();

    // 注册订阅者 作用 : 注册订阅者和路由
    this._subscriber.register();
    //初始化路由
    this._route.init();
    // 获取任务数据 作用 : 获取任务数据
    TimeTaskModule.api.timeTaskInfo(() => {
      completedCallback && completedCallback();
    });
  }

  // 保存键 作用 : 保存键
  protected saveKey(): string {
    return this.constructor.name;
  }
}
